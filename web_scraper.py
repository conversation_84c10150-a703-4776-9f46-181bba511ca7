#!/usr/bin/env python3
"""
Simple Web Scraper for Power Plant Data

This module handles Google search and web scraping with caching.
"""

import requests
from bs4 import BeautifulSoup
import time
from typing import List
from cache_manager import cache_manager

def search_google(query: str, num_results: int = 5) -> List[str]:
    """Search Google and return top URLs."""
    print(f"🔍 Searching Google for: {query}")

    try:
        # Use DuckDuckGo as alternative to Google (more reliable)
        search_url = f"https://duckduckgo.com/html/?q={query}"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(search_url, headers=headers, timeout=15)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract URLs from DuckDuckGo search results
        urls = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            if href.startswith('http') and 'duckduckgo.com' not in href:
                # Clean URL
                if '?' in href:
                    clean_url = href.split('?')[0]
                else:
                    clean_url = href

                if clean_url not in urls and len(clean_url) > 10:
                    urls.append(clean_url)
                    if len(urls) >= num_results:
                        break

        # If DuckDuckGo fails, try Google
        if not urls:
            search_url = f"https://www.google.com/search?q={query}&num={num_results}"
            response = requests.get(search_url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            for link in soup.find_all('a', href=True):
                href = link['href']
                if href.startswith('/url?q='):
                    # Extract actual URL from Google redirect
                    actual_url = href.split('/url?q=')[1].split('&')[0]
                    if actual_url.startswith('http') and 'google.com' not in actual_url:
                        urls.append(actual_url)
                        if len(urls) >= num_results:
                            break

        print(f"🔗 Found {len(urls)} URLs")
        for i, url in enumerate(urls, 1):
            print(f"{i}. {url[:80]}...")

        return urls

    except Exception as e:
        print(f"❌ Search failed: {e}")
        return []

def scrape_url(url: str) -> str:
    """Scrape content from a single URL."""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text content
        text = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        # Limit content length
        if len(text) > 10000:
            text = text[:10000]
        
        return text
        
    except Exception as e:
        print(f"❌ Failed to scrape {url}: {e}")
        return ""

def scrape_and_cache_urls(urls: List[str]) -> str:
    """Scrape URLs and cache the content."""
    all_content = []
    
    for url in urls:
        # Check cache first
        cached_content = cache_manager.get_cached_content(url)
        if cached_content:
            all_content.append(cached_content)
            continue
        
        # Scrape if not cached
        print(f"🌐 Scraping: {url[:80]}...")
        content = scrape_url(url)
        
        if content:
            cache_manager.save_content(url, content)
            all_content.append(content)
            time.sleep(1)  # Be respectful to servers
        else:
            print(f"❌ No content from {url}")
    
    combined_content = '\n\n'.join(all_content)
    print(f"📄 Total scraped content: {len(combined_content)} characters")
    return combined_content
