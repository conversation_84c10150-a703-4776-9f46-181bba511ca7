[{"auxiliary_power_consumed": null, "boiler_type": "Not specified", "capacity": "660, 660", "capacity_unit": null, "capex_required_renovation_closed_cycle": null, "capex_required_renovation_closed_cycle_unit": null, "capex_required_renovation_open_cycle": null, "capex_required_renovation_open_cycle_unit": null, "capex_required_retrofit": null, "capex_required_retrofit_unit": null, "closed_cylce_gas_turbine_efficency": null, "combined_cycle_heat_rate": null, "commencement_date": null, "efficiency_loss_cofiring": null, "emission_factor": [{"value": "The amount by weight of CO2 emissions released by the unit/plant per kiliwatt-hour (kWh) generation of energy. Represented in (kg CO2e/kWh)", "year": "Year of the emission factor data"}], "fuel_type": [{"fuel": "Coal", "type": "Supercritical coal-fired", "years_percentage": {"2012": "100%"}}], "gcv_biomass": null, "gcv_biomass_unit": null, "gcv_coal": "Not specified", "gcv_coal_unit": null, "gcv_natural_gas": "Not specified", "gcv_natural_gas_unit": null, "gross_power_generation": [{"value": "Total energy generated by the unit/plant in a Financial Year", "year": "Year of the power generation data"}], "heat_rate": "Not specified", "heat_rate_unit": null, "open_cycle_gas_turbine_efficency": null, "open_cycle_heat_rate": null, "PAF": null, "plant_id": null, "plf": null, "ppa_details": [{"capacity": "Contracted capacity under PPA (in MW)", "capacity_unit": "Unit for capacity, typically 'MW'", "end_date": "PPA end date (format: yyyy-mm-ddThh:mm:ss.msZ)", "respondents": [{"capacity": "Capacity assigned to respondent (in MW)", "currency": "Currency of the tariff (e.g., USD, INR)", "name": "Name of the respondent (utility/buyer)", "price": "Tariff price (per unit energy)", "price_unit": "Unit for tariff price (e.g., $/kWh)"}], "start_date": "PPA start date (format: yyyy-mm-ddThh:mm:ss.msZ)", "tenure": "Duration of the PPA in years", "tenure_type": "Type of tenure (e.g., Fixed, Variable)"}], "remaining_useful_life": "2025-12-31T00:00:00.000Z", "selected_biomass_type": "Not specified", "selected_coal_type": "Not specified", "technology": "supercritical", "unit": null, "unit_efficiency": null, "unit_lifetime": "25", "unit_number": "1"}, {"auxiliary_power_consumed": [{"value": "The auxiliary (AUX) energy i.e energy consumed internally by the power plant its for its day-to-day operations. It is represented as a percentage of the gross energy generated by the plant/unnit.", "year": "Year for which auxiliary power data is reported"}], "boiler_type": null, "capacity": "1320 MW", "capacity_unit": null, "capex_required_renovation_closed_cycle": null, "capex_required_renovation_closed_cycle_unit": null, "capex_required_renovation_open_cycle": null, "capex_required_renovation_open_cycle_unit": null, "capex_required_retrofit": null, "capex_required_retrofit_unit": null, "closed_cylce_gas_turbine_efficency": null, "combined_cycle_heat_rate": null, "commencement_date": null, "efficiency_loss_cofiring": null, "emission_factor": [{"value": "The amount by weight of CO2 emissions released by the unit/plant per kiliwatt-hour (kWh) generation of energy. Represented in (kg CO2e/kWh)", "year": "Year of the emission factor data"}], "fuel_type": [{"fuel": "Coal", "type": "Supercritical coal-fired", "years_percentage": {"2012": "100"}}], "gcv_biomass": null, "gcv_biomass_unit": null, "gcv_coal": null, "gcv_coal_unit": null, "gcv_natural_gas": null, "gcv_natural_gas_unit": null, "gross_power_generation": [{"value": "Total energy generated by the unit/plant in a Financial Year", "year": "Year of the power generation data"}], "heat_rate": null, "heat_rate_unit": null, "open_cycle_gas_turbine_efficency": null, "open_cycle_heat_rate": null, "PAF": null, "plant_id": null, "plf": [{"value": "The yearly Plant Load factor (PLF) for every unit of the plant. Represented in %.", "year": "Year of the PLF data"}], "ppa_details": [{"capacity": "Contracted capacity under PPA (in MW)", "capacity_unit": "Unit for capacity, typically 'MW'", "end_date": "PPA end date (format: yyyy-mm-ddThh:mm:ss.msZ)", "respondents": [{"capacity": "Capacity assigned to respondent (in MW)", "currency": "Currency of the tariff (e.g., USD, INR)", "name": "Name of the respondent (utility/buyer)", "price": "Tariff price (per unit energy)", "price_unit": "Unit for tariff price (e.g., $/kWh)"}], "start_date": "PPA start date (format: yyyy-mm-ddThh:mm:ss.msZ)", "tenure": "Duration of the PPA in years", "tenure_type": "Type of tenure (e.g., Fixed, Variable)"}], "remaining_useful_life": "2025-12-31T00:00:00.000Z", "selected_biomass_type": "Not specified", "selected_coal_type": "Not specified", "technology": "Coal - Super Critical", "unit": null, "unit_efficiency": "Unit specific efficiency of the coal power plant unit", "unit_lifetime": "25", "unit_number": "2"}]