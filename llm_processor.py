#!/usr/bin/env python3
"""
LLM Processor for Power Plant Data Extraction

This module handles OpenAI API calls for data extraction.
"""

import openai
import json
import os
from typing import Dict, List, Optional

# Initialize LLM clients
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
GROQ_API_KEY = os.getenv('GROQ_API_KEY')

# Try Groq first (faster and cheaper), fallback to OpenAI
if GROQ_API_KEY:
    try:
        import groq
        groq_client = groq.Groq(api_key=GROQ_API_KEY)
        print("🚀 Using Groq API for LLM processing")
        USE_GROQ = True
    except ImportError:
        print("⚠️ Groq library not installed, using OpenAI")
        USE_GROQ = False
        groq_client = None
else:
    USE_GROQ = False
    groq_client = None

if OPENAI_API_KEY:
    openai_client = openai.OpenAI(api_key=OPENAI_API_KEY)
    print("🤖 OpenAI API ready as backup")
else:
    openai_client = None

if not GROQ_API_KEY and not OPENAI_API_KEY:
    raise ValueError("Please set either GROQ_API_KEY or OPENAI_API_KEY environment variable")

def extract_data_with_llm(plant_name: str, content: str, schema: Dict, data_type: str) -> Dict:
    """Extract data using LLM (Groq or OpenAI)."""

    # Create a comprehensive prompt that demands ALL fields
    prompt = f"""Extract COMPLETE power plant data for "{plant_name}" from the provided content.

DATA TYPE: {data_type.upper()}

REQUIRED SCHEMA - EXTRACT ALL FIELDS:
{json.dumps(schema, indent=2)}

CONTENT:
{content[:12000]}

CRITICAL INSTRUCTIONS:
1. Extract EVERY field from the schema - do not skip any fields
2. Return JSON with ALL schema fields, even if some are null
3. Extract ONLY real data values, never field descriptions
4. For missing data, use null (not empty strings or descriptions)
5. For arrays, return proper array structures with real data
6. For dates, use format: yyyy-mm-ddThh:mm:ss.msZ
7. For percentages, use numbers (e.g., 85.5 for 85.5%)
8. For capacities, use numbers in MW (e.g., 1320 for 1320 MW)
9. For efficiency, PLF, PAF - use percentage numbers
10. Include ALL nested objects and arrays as per schema

EXAMPLE OUTPUT STRUCTURE:
- Include every field name from the schema
- Use real values where found, null where not found
- Maintain exact schema structure

Return complete JSON with ALL fields:"""

    # Try Groq first (faster and cheaper)
    if USE_GROQ and groq_client:
        try:
            print(f"🚀 Using Groq for {data_type} extraction...")
            response = groq_client.chat.completions.create(
                model="llama3-8b-8192",  # Fast Groq model
                messages=[
                    {"role": "system", "content": "You are a precise data extractor. Extract only real data values from content, never field descriptions."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.1
            )

            result_text = response.choices[0].message.content.strip()

            # Parse JSON
            if '{' in result_text and '}' in result_text:
                start = result_text.find('{')
                end = result_text.rfind('}') + 1
                json_str = result_text[start:end]
                extracted_data = json.loads(json_str)

                # Validate extracted data
                validated_data = validate_extracted_data(extracted_data, schema)
                return validated_data

        except Exception as e:
            print(f"⚠️ Groq extraction failed: {e}, trying OpenAI...")

    # Fallback to OpenAI
    if openai_client:
        try:
            print(f"🤖 Using OpenAI for {data_type} extraction...")
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a precise data extractor. Extract only real data values from content, never field descriptions."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.1
            )

            result_text = response.choices[0].message.content.strip()

            # Parse JSON
            if '{' in result_text and '}' in result_text:
                start = result_text.find('{')
                end = result_text.rfind('}') + 1
                json_str = result_text[start:end]
                extracted_data = json.loads(json_str)

                # Validate extracted data
                validated_data = validate_extracted_data(extracted_data, schema)
                return validated_data

        except Exception as e:
            print(f"❌ OpenAI extraction failed: {e}")

    print(f"❌ All LLM extraction methods failed for {data_type}")
    return {}

def validate_extracted_data(data: Dict, schema: Dict) -> Dict:
    """Validate and clean extracted data - more permissive to keep valid data."""
    validated = {}

    for field, value in data.items():
        if field not in schema:
            continue

        if value is None:
            continue

        # Handle empty strings and empty collections
        if value == "" or value == [] or value == {}:
            continue

        # Skip obvious description text (more restrictive check)
        if isinstance(value, str):
            # Only skip if it's clearly a field description
            if any(phrase in value.lower() for phrase in [
                "represented in", "format:", "e.g.", "such as", "typically",
                "the amount by weight", "total energy generated",
                "duration of", "capacity assigned", "name of the",
                "unit for", "currency of", "tariff price"
            ]) and len(value) > 50:  # Only skip long descriptive text
                continue

            # Skip obvious placeholder values
            if value.lower() in ["not specified", "n/a", "na", "null", "not available", "none"]:
                continue

        # Special handling for units_id field
        if field == 'units_id':
            # Keep the raw value for units_id, let the main pipeline parse it
            validated[field] = value
            continue

        # Handle arrays more carefully
        if isinstance(value, list):
            cleaned_array = []
            for item in value:
                if isinstance(item, dict):
                    cleaned_item = {}
                    for k, v in item.items():
                        # Be more permissive with nested values
                        if v is not None and v != "" and v != []:
                            # Only skip if it's clearly a description
                            if isinstance(v, str) and any(phrase in v.lower() for phrase in [
                                "represented in", "format:", "e.g."
                            ]) and len(v) > 30:
                                continue
                            cleaned_item[k] = v
                    if cleaned_item:
                        cleaned_array.append(cleaned_item)
                elif item is not None and item != "":
                    # Keep non-empty items
                    if not (isinstance(item, str) and any(phrase in item.lower() for phrase in [
                        "represented in", "format:", "e.g."
                    ]) and len(item) > 30):
                        cleaned_array.append(item)

            if cleaned_array:
                validated[field] = cleaned_array
        else:
            validated[field] = value

    return validated

def search_missing_fields(plant_name: str, missing_fields: List[str], schema: Dict, data_type: str) -> Dict:
    """Search for specific missing fields."""
    from web_scraper import search_google, scrape_and_cache_urls
    
    if not missing_fields:
        return {}
    
    print(f"🔍 Searching for missing {data_type} fields: {missing_fields}")
    
    # Create targeted search query
    field_descriptions = []
    for field in missing_fields:
        if field in schema:
            field_desc = str(schema[field])
            if isinstance(schema[field], list) and len(schema[field]) > 0:
                field_desc = str(schema[field][0])
            field_descriptions.append(field_desc.split('.')[0])  # Take first sentence
    
    query = f'"{plant_name}" {" ".join(field_descriptions[:3])}'  # Limit query length
    
    # Search and scrape
    urls = search_google(query, num_results=3)
    if not urls:
        return {}
    
    content = scrape_and_cache_urls(urls)
    if not content:
        return {}
    
    # Extract only the missing fields
    mini_schema = {field: schema[field] for field in missing_fields if field in schema}
    extracted = extract_data_with_llm(plant_name, content, mini_schema, data_type)
    
    return extracted
