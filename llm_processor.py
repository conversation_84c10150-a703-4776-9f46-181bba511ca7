#!/usr/bin/env python3
"""
LLM Processor for Power Plant Data Extraction

This module handles OpenAI API calls for data extraction.
"""

import openai
import json
import os
from typing import Dict, List, Optional

# Initialize OpenAI client
API_KEY = os.getenv('OPENAI_API_KEY')
if not API_KEY:
    raise ValueError("Please set OPENAI_API_KEY environment variable")

client = openai.OpenAI(api_key=API_KEY)

def extract_data_with_llm(plant_name: str, content: str, schema: Dict, data_type: str) -> Dict:
    """Extract data using OpenAI LLM."""
    
    prompt = f"""Extract power plant data for "{plant_name}" from the provided content.

DATA TYPE: {data_type.upper()}

SCHEMA TO FILL:
{json.dumps(schema, indent=2)}

CONTENT:
{content[:8000]}

INSTRUCTIONS:
1. Extract ONLY real data values, not descriptions
2. Return JSON matching the exact schema structure
3. If a field cannot be found, set it to null
4. For arrays, return proper array structures
5. For dates, use format: yyyy-mm-ddThh:mm:ss.msZ
6. Extract actual numbers, names, dates - NO field descriptions

Return only the JSON object:"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a precise data extractor. Extract only real data values from content, never field descriptions."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=2000,
            temperature=0.1
        )
        
        result_text = response.choices[0].message.content.strip()
        
        # Parse JSON
        if '{' in result_text and '}' in result_text:
            start = result_text.find('{')
            end = result_text.rfind('}') + 1
            json_str = result_text[start:end]
            extracted_data = json.loads(json_str)
            
            # Validate extracted data
            validated_data = validate_extracted_data(extracted_data, schema)
            return validated_data
        
        return {}
        
    except Exception as e:
        print(f"❌ LLM extraction failed: {e}")
        return {}

def validate_extracted_data(data: Dict, schema: Dict) -> Dict:
    """Validate and clean extracted data."""
    validated = {}

    for field, value in data.items():
        if field not in schema:
            continue

        if value is None or value == "":
            continue

        # Skip obvious description text
        if isinstance(value, str):
            if any(phrase in value.lower() for phrase in [
                "represented in", "format:", "e.g.", "such as", "typically",
                "year of", "amount by weight", "total energy generated",
                "duration of", "capacity assigned", "name of the",
                "unit for", "currency of", "tariff price"
            ]):
                continue

            # Skip placeholder values
            if value.lower() in ["not specified", "n/a", "na", "null", "not available"]:
                continue

        # Special handling for units_id field
        if field == 'units_id':
            # Keep the raw value for units_id, let the main pipeline parse it
            validated[field] = value
            continue

        # Skip arrays with description objects
        if isinstance(value, list):
            cleaned_array = []
            for item in value:
                if isinstance(item, dict):
                    cleaned_item = {}
                    for k, v in item.items():
                        if isinstance(v, str) and not any(phrase in v.lower() for phrase in [
                            "represented in", "format:", "e.g.", "such as", "typically"
                        ]):
                            cleaned_item[k] = v
                        elif not isinstance(v, str):
                            cleaned_item[k] = v
                    if cleaned_item:
                        cleaned_array.append(cleaned_item)
                elif not isinstance(item, str) or not any(phrase in item.lower() for phrase in [
                    "represented in", "format:", "e.g."
                ]):
                    cleaned_array.append(item)

            if cleaned_array:
                validated[field] = cleaned_array
        else:
            validated[field] = value

    return validated

def search_missing_fields(plant_name: str, missing_fields: List[str], schema: Dict, data_type: str) -> Dict:
    """Search for specific missing fields."""
    from web_scraper import search_google, scrape_and_cache_urls
    
    if not missing_fields:
        return {}
    
    print(f"🔍 Searching for missing {data_type} fields: {missing_fields}")
    
    # Create targeted search query
    field_descriptions = []
    for field in missing_fields:
        if field in schema:
            field_desc = str(schema[field])
            if isinstance(schema[field], list) and len(schema[field]) > 0:
                field_desc = str(schema[field][0])
            field_descriptions.append(field_desc.split('.')[0])  # Take first sentence
    
    query = f'"{plant_name}" {" ".join(field_descriptions[:3])}'  # Limit query length
    
    # Search and scrape
    urls = search_google(query, num_results=3)
    if not urls:
        return {}
    
    content = scrape_and_cache_urls(urls)
    if not content:
        return {}
    
    # Extract only the missing fields
    mini_schema = {field: schema[field] for field in missing_fields if field in schema}
    extracted = extract_data_with_llm(plant_name, content, mini_schema, data_type)
    
    return extracted
