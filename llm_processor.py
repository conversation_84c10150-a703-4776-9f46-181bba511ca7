#!/usr/bin/env python3
"""
LLM Processor for Power Plant Data Extraction

This module handles OpenAI API calls for data extraction.
"""

import openai
import json
import os
from typing import Dict, List, Optional

# Initialize OpenAI client (primary LLM as per user request)
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

if OPENAI_API_KEY:
    openai_client = openai.OpenAI(api_key=OPENAI_API_KEY)
    print("🤖 Using OpenAI GPT-4o-mini for LLM processing")
else:
    raise ValueError("Please set OPENAI_API_KEY environment variable")

def extract_data_with_llm(plant_name: str, content: str, schema: Dict, data_type: str) -> Dict:
    """Extract data using LLM (Groq or OpenAI)."""

    # Create a comprehensive prompt that demands ALL fields
    prompt = f"""Extract COMPLETE power plant data for "{plant_name}" from the provided content.

DATA TYPE: {data_type.upper()}

REQUIRED SCHEMA - EXTRACT ALL FIELDS:
{json.dumps(schema, indent=2)}

CONTENT:
{content[:12000]}

CRITICAL INSTRUCTIONS:
1. Extract EVERY field from the schema - do not skip any fields
2. Return JSON with ALL schema fields, even if some are null
3. Extract ONLY real data values, never field descriptions
4. For missing data, use null (not empty strings or descriptions)
5. For arrays, return proper array structures with real data
6. For dates, use format: yyyy-mm-ddThh:mm:ss.msZ
7. For percentages, use numbers (e.g., 85.5 for 85.5%)
8. For capacities, use numbers in MW (e.g., 1320 for 1320 MW)
9. For efficiency, PLF, PAF - use percentage numbers
10. Include ALL nested objects and arrays as per schema

EXAMPLE OUTPUT STRUCTURE:
- Include every field name from the schema
- Use real values where found, null where not found
- Maintain exact schema structure

Return complete JSON with ALL fields:"""

    # Use OpenAI GPT-4o-mini as primary LLM
    try:
        print(f"🤖 Using OpenAI GPT-4o-mini for {data_type} extraction...")
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a precise data extractor. Extract only real data values from content, never field descriptions."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=3000,
            temperature=0.1
        )

        result_text = response.choices[0].message.content.strip()

        # Parse JSON
        if '{' in result_text and '}' in result_text:
            start = result_text.find('{')
            end = result_text.rfind('}') + 1
            json_str = result_text[start:end]
            extracted_data = json.loads(json_str)

            # Validate extracted data
            validated_data = validate_extracted_data(extracted_data, schema)
            return validated_data

        return {}

    except Exception as e:
        print(f"❌ OpenAI extraction failed: {e}")
        return {}

def validate_extracted_data(data: Dict, schema: Dict) -> Dict:
    """Validate and clean extracted data - more permissive to keep valid data."""
    validated = {}

    for field, value in data.items():
        if field not in schema:
            continue

        if value is None:
            continue

        # Handle empty strings and empty collections
        if value == "" or value == [] or value == {}:
            continue

        # Skip obvious description text (more restrictive check)
        if isinstance(value, str):
            # Only skip if it's clearly a field description
            if any(phrase in value.lower() for phrase in [
                "represented in", "format:", "e.g.", "such as", "typically",
                "the amount by weight", "total energy generated",
                "duration of", "capacity assigned", "name of the",
                "unit for", "currency of", "tariff price"
            ]) and len(value) > 50:  # Only skip long descriptive text
                continue

            # Skip obvious placeholder values
            if value.lower() in ["not specified", "n/a", "na", "null", "not available", "none"]:
                continue

        # Special handling for units_id field
        if field == 'units_id':
            # Keep the raw value for units_id, let the main pipeline parse it
            validated[field] = value
            continue

        # Handle arrays more carefully
        if isinstance(value, list):
            cleaned_array = []
            for item in value:
                if isinstance(item, dict):
                    cleaned_item = {}
                    for k, v in item.items():
                        # Be more permissive with nested values
                        if v is not None and v != "" and v != []:
                            # Only skip if it's clearly a description
                            if isinstance(v, str) and any(phrase in v.lower() for phrase in [
                                "represented in", "format:", "e.g."
                            ]) and len(v) > 30:
                                continue
                            cleaned_item[k] = v
                    if cleaned_item:
                        cleaned_array.append(cleaned_item)
                elif item is not None and item != "":
                    # Keep non-empty items
                    if not (isinstance(item, str) and any(phrase in item.lower() for phrase in [
                        "represented in", "format:", "e.g."
                    ]) and len(item) > 30):
                        cleaned_array.append(item)

            if cleaned_array:
                validated[field] = cleaned_array
        else:
            validated[field] = value

    return validated

def search_missing_fields(plant_name: str, missing_fields: List[str], schema: Dict, data_type: str) -> Dict:
    """Search for missing fields using plant name + field description queries."""
    from web_scraper import search_google, scrape_and_cache_urls

    if not missing_fields:
        return {}

    print(f"🔍 Searching for {len(missing_fields)} missing {data_type} fields...")

    extracted_data = {}

    # Search for each missing field individually using field description
    for field in missing_fields:
        if field not in schema:
            continue

        field_description = schema[field]

        # Handle nested schema descriptions
        if isinstance(field_description, list) and len(field_description) > 0:
            field_description = str(field_description[0])
        elif isinstance(field_description, dict):
            field_description = f"{field} details"
        else:
            field_description = str(field_description)

        # Create query: plant name + field description
        query = f'"{plant_name}" {field_description}'
        print(f"   🔍 Searching for {field}: {query[:60]}...")

        # Search and scrape
        urls = search_google(query, num_results=2)
        if not urls:
            continue

        content = scrape_and_cache_urls(urls)
        if not content:
            continue

        # Extract data for this specific field only
        single_field_schema = {field: schema[field]}
        field_data = extract_data_with_llm(plant_name, content, single_field_schema, data_type)

        if field_data and field in field_data and field_data[field] is not None:
            extracted_data[field] = field_data[field]
            print(f"   ✅ Found data for {field}")

    return extracted_data
