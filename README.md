# Power Plant Data Extraction Pipeline

A clean, fast pipeline for extracting power plant data following the exact procedure:

1. **Take plant name as input**
2. **Search Google for first 5 links**
3. **Scrape and save content in cache memory**
4. **Fill org details JSON with cached content**
5. **For plant level: check cache first, then search for missing fields**
6. **For unit level: check cache first, then search for missing fields**

## Features

- ✅ **No hardcoded values** - completely dynamic
- ✅ **Cache-first approach** - efficient content reuse
- ✅ **Real data extraction** - no schema descriptions
- ✅ **OpenAI API integration** - accurate LLM processing
- ✅ **Clean JSON output** - matches exact schemas

## Files Structure

```
├── main_pipeline.py          # Main pipeline orchestrator
├── cache_manager.py          # Cache management for scraped content
├── web_scraper.py           # Google search and web scraping
├── llm_processor.py         # OpenAI LLM data extraction
├── run_pipeline.py          # Easy runner with environment setup
├── org_details_desc.py      # Organization schema
├── plant_details_desc.py    # Plant schema
├── unit_details_desc.py     # Unit schema
├── requirements.txt         # Dependencies
└── README.md               # This file
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Get your OpenAI API key from: https://platform.openai.com/api-keys

## Usage

### Option 1: Easy Run (Recommended)
```bash
python run_pipeline.py
```
- Will prompt for OpenAI API key
- Will prompt for plant name
- Handles everything automatically

### Option 2: Manual Run
```bash
export OPENAI_API_KEY="your-api-key-here"
python main_pipeline.py
```

## How It Works

### Step 1: Initial Search and Caching
- Clears cache memory
- Searches Google for "{plant_name} power plant"
- Gets first 5 links
- Scrapes all content and saves to cache

### Step 2: Organization Details
- Extracts org data from cached content using LLM
- Identifies missing fields
- Searches web for missing fields only

### Step 3: Plant Details
- Checks cache first for plant data
- Uses cached content if available
- Searches for missing fields only

### Step 4: Unit Details
- Checks cache first for unit data
- Processes each unit individually
- Searches for missing fields only

### Step 5: Save Results
- Saves to `output/` directory:
  - `org_details_{plant_name}.json`
  - `plant_details_{plant_name}.json`
  - `unit_details_{plant_name}.json`

## Example Output

For "Jhajjar Power Plant":

```json
// org_details_Jhajjar Power Plant.json
{
  "organization_name": "Jhajjar Power Limited",
  "country_name": "India",
  "cfpp_type": "Private",
  "currency_in": "INR",
  "plants_count": 1
}

// plant_details_Jhajjar Power Plant.json
[{
  "name": "Jhajjar Power Plant",
  "plant_type": "Coal",
  "capacity": "1320",
  "units_id": [1, 2]
}]

// unit_details_Jhajjar Power Plant.json
[{
  "unit_number": "1",
  "capacity": "660",
  "technology": "supercritical",
  "fuel_type": [{"fuel": "Coal", "type": "Bituminous"}]
}]
```

## Performance

- **Execution Time**: 5-15 minutes (depending on plant complexity)
- **Cache Efficiency**: Reuses scraped content across all levels
- **API Efficiency**: Minimal LLM calls, targeted searches
- **Accuracy**: Real data values, no schema descriptions

## Troubleshooting

1. **"OpenAI API key not set"**
   - Use `run_pipeline.py` or set `OPENAI_API_KEY` environment variable

2. **"No search results found"**
   - Check internet connection
   - Try different plant name variations

3. **"No content scraped"**
   - Some websites may block scraping
   - Pipeline will continue with available content

## Notes

- The pipeline is completely generic - works with any power plant name
- No hardcoded URLs or plant-specific logic
- Cache is cleared at start of each run for fresh data
- All extracted data is validated to ensure real values, not descriptions
