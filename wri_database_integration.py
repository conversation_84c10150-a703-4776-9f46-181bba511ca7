import pandas as pd
import os
import json
from fuzzywuzzy import fuzz, process
from typing import Dict, List, Optional, Tuple
from utils import search_google, scrape_text_from_links_with_cache, call_llm, build_prompt

class WRIDatabase:
    """
    WRI Global Power Plant Database integration for fast and accurate power plant data extraction.
    This provides a hybrid approach: primary data from WRI database, missing fields from web search.
    """
    
    def __init__(self, csv_path: str = "wri_global_power_plant_database.csv"):
        """Initialize WRI database integration."""
        self.csv_path = csv_path
        self.df = None
        self.load_database()
    
    def load_database(self):
        """Load the WRI database CSV file."""
        try:
            if os.path.exists(self.csv_path):
                print(f"📊 Loading WRI Global Power Plant Database from {self.csv_path}")
                self.df = pd.read_csv(self.csv_path)
                print(f"✅ Loaded {len(self.df)} power plants from WRI database")
            else:
                print(f"❌ WRI database file not found: {self.csv_path}")
                self.df = pd.DataFrame()
        except Exception as e:
            print(f"❌ Error loading WRI database: {e}")
            self.df = pd.DataFrame()
    
    def search_plant_by_name(self, plant_name: str, country: str = "India", threshold: int = 80) -> Optional[Dict]:
        """
        Search for a power plant by name using fuzzy matching.
        
        Args:
            plant_name: Name of the power plant to search for
            country: Country to filter by (default: India)
            threshold: Minimum similarity score (0-100)
            
        Returns:
            Dictionary with plant data if found, None otherwise
        """
        if self.df is None or self.df.empty:
            print("❌ WRI database not loaded")
            return None
        
        # Filter by country first
        country_df = self.df[self.df['country_long'].str.contains(country, case=False, na=False)]
        
        if country_df.empty:
            print(f"❌ No plants found in {country}")
            return None
        
        # Get all plant names for fuzzy matching
        plant_names = country_df['name'].dropna().tolist()
        
        # Find best match using fuzzy string matching
        best_match = process.extractOne(plant_name, plant_names, scorer=fuzz.token_sort_ratio)
        
        if best_match and best_match[1] >= threshold:
            matched_name = best_match[0]
            matched_row = country_df[country_df['name'] == matched_name].iloc[0]
            
            print(f"🎯 Found WRI match: '{matched_name}' (similarity: {best_match[1]}%)")
            return matched_row.to_dict()
        else:
            print(f"❌ No WRI match found for '{plant_name}' (best: {best_match[1] if best_match else 0}%)")
            return None
    
    def map_wri_to_org_schema(self, wri_data: Dict) -> Dict:
        """
        Map WRI database fields to our organization schema.
        
        Args:
            wri_data: Raw data from WRI database
            
        Returns:
            Dictionary mapped to our org schema
        """
        # Determine plant type from primary fuel
        fuel_mapping = {
            'Coal': 'Coal',
            'Gas': 'Natural Gas', 
            'Oil': 'Oil',
            'Nuclear': 'Nuclear',
            'Hydro': 'Hydro',
            'Wind': 'Wind',
            'Solar': 'Solar',
            'Biomass': 'Biomass',
            'Geothermal': 'Geothermal',
            'Waste': 'Waste'
        }
        
        primary_fuel = wri_data.get('primary_fuel', '')
        plant_type = fuel_mapping.get(primary_fuel, primary_fuel)
        
        # Determine ownership type (simplified logic)
        owner = str(wri_data.get('owner', '')).lower()
        if any(keyword in owner for keyword in ['government', 'state', 'national', 'public']):
            cfpp_type = 'public'
        elif any(keyword in owner for keyword in ['private', 'ltd', 'limited', 'corp', 'inc']):
            cfpp_type = 'private'
        else:
            cfpp_type = 'other'
        
        # Map to our schema
        org_data = {
            'cfpp_type': cfpp_type,
            'country_name': wri_data.get('country_long', ''),
            'currency_in': 'INR' if wri_data.get('country_long') == 'India' else 'USD',  # Default mapping
            'financial_year': None,  # Not available in WRI
            'organization_name': wri_data.get('owner', ''),
            'plants_count': 1,  # WRI is per plant, so count is 1
            'plant_types': [plant_type] if plant_type else [],
            'ppa_flag': None,  # Not available in WRI
            'province': None  # Not available in WRI
        }
        
        return org_data
    
    def map_wri_to_plant_schema(self, wri_data: Dict) -> Dict:
        """
        Map WRI database fields to our plant schema.
        
        Args:
            wri_data: Raw data from WRI database
            
        Returns:
            Dictionary mapped to our plant schema
        """
        # Extract coordinates
        lat = wri_data.get('latitude')
        lon = wri_data.get('longitude')
        
        # Convert capacity to float if possible
        capacity = wri_data.get('capacity_mw')
        if capacity and str(capacity).replace('.', '').isdigit():
            capacity = float(capacity)
        
        # Determine plant type
        primary_fuel = wri_data.get('primary_fuel', '')
        fuel_mapping = {
            'Coal': 'coal-fired',
            'Gas': 'gas-fired', 
            'Oil': 'oil-fired',
            'Nuclear': 'nuclear',
            'Hydro': 'hydroelectric',
            'Wind': 'wind',
            'Solar': 'solar',
            'Biomass': 'biomass',
            'Geothermal': 'geothermal'
        }
        plant_type = fuel_mapping.get(primary_fuel, primary_fuel.lower() if primary_fuel else '')
        
        plant_data = {
            'lat': lat,
            'long': lon,
            'name': wri_data.get('name', ''),
            'plant_type': plant_type,
            'capacity': capacity,
            'plant_id': wri_data.get('gppd_idnr'),
            'plant_address': None,  # Not available in WRI
            'ppa_details': [],  # Not available in WRI
            'grid_connectivity_maps': [],  # Not available in WRI
            'units_id': [1]  # Default to single unit, will be updated if more info available
        }
        
        return plant_data
    
    def map_wri_to_unit_schema(self, wri_data: Dict, unit_id: int = 1) -> Dict:
        """
        Map WRI database fields to our unit schema.
        
        Args:
            wri_data: Raw data from WRI database
            unit_id: Unit identifier
            
        Returns:
            Dictionary mapped to our unit schema
        """
        # Extract generation data
        generation_data = []
        for year in range(2013, 2020):  # WRI has data for 2013-2019
            gen_key = f'generation_gwh_{year}'
            if gen_key in wri_data and wri_data[gen_key] is not None:
                try:
                    value = float(wri_data[gen_key])
                    generation_data.append({
                        'value': value * 1000,  # Convert GWh to MWh
                        'year': str(year)
                    })
                except (ValueError, TypeError):
                    continue
        
        # Extract fuel information
        primary_fuel = wri_data.get('primary_fuel', '')
        fuel_type_data = []
        if primary_fuel:
            fuel_type_data.append({
                'fuel': primary_fuel,
                'type': primary_fuel,  # Simplified
                'years_percentage': {'2020': '100'}  # Default assumption
            })
        
        # Extract capacity
        capacity = wri_data.get('capacity_mw')
        if capacity and str(capacity).replace('.', '').isdigit():
            capacity = float(capacity)
        
        # Determine technology based on fuel type
        technology_mapping = {
            'Coal': 'Super Critical',  # Default assumption
            'Gas': 'Combined Cycle',
            'Oil': 'Simple Cycle',
            'Nuclear': 'Pressurized Water Reactor',
            'Hydro': 'Conventional',
            'Wind': 'Horizontal Axis',
            'Solar': 'Photovoltaic'
        }
        technology = technology_mapping.get(primary_fuel, '')
        
        unit_data = {
            'auxiliary_power_consumed': [],  # Not available in WRI
            'capacity': capacity,
            'commissioning_date': None,  # WRI has commissioning_year but not full date
            'emission_factor': [],  # Not available in WRI
            'fuel_type': fuel_type_data,
            'gcv_coal': None,  # Not available in WRI
            'gcv_natural_gas_unit': None,  # Not available in WRI
            'gross_power_generation': generation_data,
            'net_power_generation': [],  # Not available in WRI
            'plant_id': wri_data.get('gppd_idnr'),
            'plf': [],  # Not available in WRI
            'ppa_details': [],  # Not available in WRI
            'remaining_useful_life': None,  # Not available in WRI
            'selected_biomass_type': None,  # Not available in WRI
            'selected_coal_type': None,  # Not available in WRI
            'technology': technology,
            'unit': None,  # Not available in WRI
            'unit_efficiency': None,  # Not available in WRI
            'unit_lifetime': None,  # Not available in WRI
            'unit_number': str(unit_id)
        }
        
        # Add commissioning year if available
        comm_year = wri_data.get('commissioning_year')
        if comm_year and str(comm_year).isdigit():
            unit_data['commissioning_date'] = f"{int(comm_year)}-01-01T00:00:00.000Z"
        
        return unit_data

def get_wri_plant_data(plant_name: str, country: str = "India") -> Tuple[Optional[Dict], Optional[Dict], Optional[List[Dict]]]:
    """
    Get comprehensive plant data from WRI database.
    
    Args:
        plant_name: Name of the power plant
        country: Country to search in
        
    Returns:
        Tuple of (org_data, plant_data, units_data)
    """
    wri_db = WRIDatabase()
    
    # Search for the plant
    wri_plant = wri_db.search_plant_by_name(plant_name, country)
    
    if not wri_plant:
        return None, None, None
    
    # Map to our schemas
    org_data = wri_db.map_wri_to_org_schema(wri_plant)
    plant_data = wri_db.map_wri_to_plant_schema(wri_plant)
    unit_data = wri_db.map_wri_to_unit_schema(wri_plant)
    
    print(f"✅ Successfully extracted WRI data for {plant_name}")
    print(f"   Organization: {org_data.get('organization_name')}")
    print(f"   Plant Type: {plant_data.get('plant_type')}")
    print(f"   Capacity: {plant_data.get('capacity')} MW")
    print(f"   Location: {plant_data.get('lat')}, {plant_data.get('long')}")
    
    return org_data, plant_data, [unit_data]
