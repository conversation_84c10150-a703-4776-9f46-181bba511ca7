#!/usr/bin/env python3
"""
Run the power plant pipeline with environment setup.
"""

import os
import sys

def setup_environment():
    """Setup environment variables."""
    # Set OpenAI API key (user should replace this with their actual key)
    api_key = input("Enter your OpenAI API key: ").strip()
    
    if not api_key:
        print("❌ OpenAI API key is required!")
        return False
    
    os.environ['OPENAI_API_KEY'] = api_key
    return True

def main():
    """Main function."""
    print("🚀 Power Plant Data Extraction Pipeline")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        return
    
    # Import and run the main pipeline
    try:
        from main_pipeline import PowerPlantPipeline
        
        # Get plant name
        plant_name = input("Enter power plant name: ").strip()
        
        if not plant_name:
            print("❌ Plant name is required!")
            return
        
        # Run pipeline
        pipeline = PowerPlantPipeline()
        results = pipeline.run_pipeline(plant_name)
        
        if results:
            print(f"\n✅ SUCCESS: Pipeline completed for {plant_name}")
            print(f"📁 Results saved in 'output/' directory")
        else:
            print(f"\n❌ FAILED: Pipeline failed for {plant_name}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
