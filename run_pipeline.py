#!/usr/bin/env python3
"""
Run the power plant pipeline with environment setup.
"""

import os
import sys

def setup_environment():
    """Setup environment variables with provided API keys."""
    # Set API keys directly
    os.environ['SCRAPER_API_KEY'] = 'b4aedbdc7dc14b48930567434b5f3f2d'
    os.environ['WEB_API_KEY'] = 'b4aedbdc7dc14b48930567434b5f3f2d'  # Same as scraper API
    os.environ['GROQ_API_KEY'] = '********************************************************'
    os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'
    os.environ['OPENAI_MODEL'] = 'gpt-4o-mini'

    print("✅ API keys configured:")
    print("   🔑 Scraper API: Ready for enhanced web scraping")
    print("   🤖 OpenAI API: Ready for LLM processing")
    print("   ⚡ Groq API: Available as alternative LLM")

    return True

def main():
    """Main function."""
    print("🚀 Power Plant Data Extraction Pipeline")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        return
    
    # Import and run the main pipeline
    try:
        from main_pipeline import PowerPlantPipeline
        
        # Get plant name
        plant_name = input("Enter power plant name: ").strip()
        
        if not plant_name:
            print("❌ Plant name is required!")
            return
        
        # Run pipeline
        pipeline = PowerPlantPipeline()
        results = pipeline.run_pipeline(plant_name)
        
        if results:
            print(f"\n✅ SUCCESS: Pipeline completed for {plant_name}")
            print(f"📁 Results saved in 'output/' directory")
        else:
            print(f"\n❌ FAILED: Pipeline failed for {plant_name}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
