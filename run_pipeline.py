#!/usr/bin/env python3
"""
Run the power plant pipeline with environment setup.
"""

import os
import sys

def setup_environment():
    """Setup environment variables."""
    # Set OpenAI API key
    openai_key = input("Enter your OpenAI API key: ").strip()

    if not openai_key:
        print("❌ OpenAI API key is required!")
        return False

    # Set Web API key (optional for better scraping)
    web_api_key = input("Enter your Web API key (optional, press Enter to skip): ").strip()

    os.environ['OPENAI_API_KEY'] = openai_key
    if web_api_key:
        os.environ['WEB_API_KEY'] = web_api_key
        print("✅ Web API key set for enhanced scraping")
    else:
        print("ℹ️ Using basic scraping (no Web API key)")

    return True

def main():
    """Main function."""
    print("🚀 Power Plant Data Extraction Pipeline")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        return
    
    # Import and run the main pipeline
    try:
        from main_pipeline import PowerPlantPipeline
        
        # Get plant name
        plant_name = input("Enter power plant name: ").strip()
        
        if not plant_name:
            print("❌ Plant name is required!")
            return
        
        # Run pipeline
        pipeline = PowerPlantPipeline()
        results = pipeline.run_pipeline(plant_name)
        
        if results:
            print(f"\n✅ SUCCESS: Pipeline completed for {plant_name}")
            print(f"📁 Results saved in 'output/' directory")
        else:
            print(f"\n❌ FAILED: Pipeline failed for {plant_name}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
