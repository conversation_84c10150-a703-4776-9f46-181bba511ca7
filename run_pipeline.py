#!/usr/bin/env python3
"""
Run the power plant pipeline with environment setup.
"""

import os
import sys

def setup_environment():
    """Setup environment variables from config or user input."""
    try:
        from config import get_api_keys, validate_config, OPENAI_MODEL

        # Try to use config file first
        api_keys = get_api_keys()

        if api_keys['openai']:
            print("✅ Using API keys from config/environment")
            os.environ['OPENAI_API_KEY'] = api_keys['openai']
            if api_keys['scraper']:
                os.environ['SCRAPER_API_KEY'] = api_keys['scraper']
                os.environ['WEB_API_KEY'] = api_keys['scraper']
                print("   🔑 Scraper API: Configured")
            if api_keys['groq']:
                os.environ['GROQ_API_KEY'] = api_keys['groq']
                print("   ⚡ Groq API: Configured")

            os.environ['OPENAI_MODEL'] = OPENAI_MODEL
            validate_config()
            return True

    except (ImportError, ValueError):
        pass

    # Fallback to user input
    print("🔑 Please provide your API keys:")

    # Get OpenAI API key (required)
    openai_key = input("Enter your OpenAI API key: ").strip()
    if not openai_key:
        print("❌ OpenAI API key is required!")
        return False
    os.environ['OPENAI_API_KEY'] = openai_key

    # Get Scraper API key (optional)
    scraper_key = input("Enter your Scraper API key (optional, press Enter to skip): ").strip()
    if scraper_key:
        os.environ['SCRAPER_API_KEY'] = scraper_key
        os.environ['WEB_API_KEY'] = scraper_key
        print("✅ Scraper API key configured")

    # Get Groq API key (optional)
    groq_key = input("Enter your Groq API key (optional, press Enter to skip): ").strip()
    if groq_key:
        os.environ['GROQ_API_KEY'] = groq_key
        print("✅ Groq API key configured")

    os.environ['OPENAI_MODEL'] = 'gpt-4o-mini'
    print("✅ Environment configured successfully!")
    return True

def main():
    """Main function."""
    print("🚀 Power Plant Data Extraction Pipeline")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        return
    
    # Import and run the main pipeline
    try:
        from main_pipeline import PowerPlantPipeline
        
        # Get plant name
        plant_name = input("Enter power plant name: ").strip()
        
        if not plant_name:
            print("❌ Plant name is required!")
            return
        
        # Run pipeline
        pipeline = PowerPlantPipeline()
        results = pipeline.run_pipeline(plant_name)
        
        if results:
            print(f"\n✅ SUCCESS: Pipeline completed for {plant_name}")
            print(f"📁 Results saved in 'output/' directory")
        else:
            print(f"\n❌ FAILED: Pipeline failed for {plant_name}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
