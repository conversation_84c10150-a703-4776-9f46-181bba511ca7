#!/usr/bin/env python3
"""
Simple Cache Manager for Power Plant Data Extraction

This module handles caching of scraped content to avoid re-scraping the same URLs.
"""

import os
import json
import hashlib
from typing import Dict, List, Optional

class CacheManager:
    """Manages cached content for scraped web pages."""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = cache_dir
        self.memory_cache = {}  # In-memory cache for current session
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_key(self, url: str) -> str:
        """Generate cache key from URL."""
        return hashlib.md5(url.encode()).hexdigest()
    
    def _get_cache_file(self, cache_key: str) -> str:
        """Get cache file path."""
        return os.path.join(self.cache_dir, f"{cache_key}.json")
    
    def get_cached_content(self, url: str) -> Optional[str]:
        """Get cached content for a URL."""
        cache_key = self._get_cache_key(url)
        
        # Check memory cache first
        if cache_key in self.memory_cache:
            print(f"📁 Using memory cache for {url[:80]}...")
            return self.memory_cache[cache_key]
        
        # Check disk cache
        cache_file = self._get_cache_file(cache_key)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    content = cache_data.get('content', '')
                    # Load into memory cache
                    self.memory_cache[cache_key] = content
                    print(f"📁 Using disk cache for {url[:80]}...")
                    return content
            except Exception as e:
                print(f"❌ Error reading cache for {url}: {e}")
        
        return None
    
    def save_content(self, url: str, content: str):
        """Save content to cache."""
        cache_key = self._get_cache_key(url)
        
        # Save to memory cache
        self.memory_cache[cache_key] = content
        
        # Save to disk cache
        cache_file = self._get_cache_file(cache_key)
        try:
            cache_data = {
                'url': url,
                'content': content,
                'timestamp': str(os.path.getmtime(cache_file) if os.path.exists(cache_file) else 0)
            }
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            print(f"💾 Cached content for {url[:80]}...")
        except Exception as e:
            print(f"❌ Error saving cache for {url}: {e}")
    
    def get_all_cached_content(self) -> str:
        """Get all cached content combined."""
        all_content = []
        
        # Get from memory cache
        for content in self.memory_cache.values():
            if content and content.strip():
                all_content.append(content)
        
        # Get from disk cache if memory is empty
        if not all_content:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    cache_file = os.path.join(self.cache_dir, filename)
                    try:
                        with open(cache_file, 'r', encoding='utf-8') as f:
                            cache_data = json.load(f)
                            content = cache_data.get('content', '')
                            if content and content.strip():
                                all_content.append(content)
                    except Exception:
                        continue
        
        combined_content = '\n\n'.join(all_content)
        print(f"📚 Combined cache content: {len(combined_content)} characters")
        return combined_content
    
    def clear_cache(self):
        """Clear all cache (memory and disk)."""
        # Clear memory cache
        self.memory_cache.clear()
        
        # Clear disk cache
        for filename in os.listdir(self.cache_dir):
            if filename.endswith('.json'):
                cache_file = os.path.join(self.cache_dir, filename)
                try:
                    os.remove(cache_file)
                except Exception:
                    pass
        
        print("🗑️ Cache cleared")
    
    def has_cached_content(self) -> bool:
        """Check if there's any cached content available."""
        # Check memory cache
        if self.memory_cache:
            return True
        
        # Check disk cache
        for filename in os.listdir(self.cache_dir):
            if filename.endswith('.json'):
                return True
        
        return False

# Global cache manager instance
cache_manager = CacheManager()
