#!/usr/bin/env python3
"""
Main Power Plant Data Extraction Pipeline

Follows the exact procedure:
1. Take plant name as input
2. Search Google for first 5 links
3. Scrape and save content in cache memory
4. Fill org details JSON with cached content
5. For plant level: check cache first, then search for missing fields
6. For unit level: check cache first, then search for missing fields
"""

import os
import json
import time
from typing import Dict, List
from cache_manager import cache_manager
from web_scraper import search_google, scrape_and_cache_urls
from llm_processor import extract_data_with_llm, search_missing_fields
from org_details_desc import ORG_SCHEMA_DESC
from plant_details_desc import PLANT_SCHEMA_DESC
from unit_details_desc import UNIT_SCHEMA_DESC

class PowerPlantPipeline:
    """Main pipeline for power plant data extraction."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def run_pipeline(self, plant_name: str) -> Dict:
        """Run the complete pipeline for a plant."""
        print(f"\n🚀 STARTING PIPELINE FOR: {plant_name}")
        print("=" * 80)
        
        start_time = time.time()
        
        # Step 1: Clear cache and search for initial content
        print("\n📋 STEP 1: Initial Search and Caching")
        print("-" * 50)
        cache_manager.clear_cache()
        
        # Search Google for first 5 links
        query = f'"{plant_name}" power plant'
        urls = search_google(query, num_results=5)
        
        if not urls:
            print("❌ No search results found")
            return {}
        
        # Scrape and cache all content
        initial_content = scrape_and_cache_urls(urls)
        
        if not initial_content:
            print("❌ No content scraped")
            return {}
        
        # Step 2: Process Organization Details
        print("\n🏢 STEP 2: Processing Organization Details")
        print("-" * 50)
        org_data = self.process_organization(plant_name, initial_content)
        
        # Step 3: Process Plant Details
        print("\n🏭 STEP 3: Processing Plant Details")
        print("-" * 50)
        plant_data = self.process_plant(plant_name)
        
        # Step 4: Process Unit Details
        print("\n⚡ STEP 4: Processing Unit Details")
        print("-" * 50)
        units_data = self.process_units(plant_name, plant_data)
        
        # Step 5: Save Results
        print("\n💾 STEP 5: Saving Results")
        print("-" * 50)
        results = {
            'organization': org_data,
            'plant': plant_data,
            'units': units_data
        }
        
        self.save_results(plant_name, results)
        
        execution_time = time.time() - start_time
        
        # Summary
        print(f"\n🎉 PIPELINE COMPLETED!")
        print("=" * 80)
        print(f"⚡ Execution Time: {execution_time:.2f} seconds ({execution_time/60:.1f} minutes)")
        print(f"🏢 Organization Fields: {len([v for v in org_data.values() if v is not None])}/{len(org_data)}")
        print(f"🏭 Plant Fields: {len([v for v in plant_data.values() if v is not None])}/{len(plant_data)}")
        print(f"⚡ Unit Fields: {len([v for v in units_data[0].values() if v is not None]) if units_data else 0}/{len(UNIT_SCHEMA_DESC)}")
        print("=" * 80)
        
        return results
    
    def process_organization(self, plant_name: str, cached_content: str) -> Dict:
        """Process organization details using cached content first, then search for missing fields."""
        print("📊 Extracting organization data from cached content...")

        # Step 1: Use cache memory to fill JSON first
        org_data = extract_data_with_llm(plant_name, cached_content, ORG_SCHEMA_DESC, "organization")

        # Ensure all schema fields are present
        for field in ORG_SCHEMA_DESC.keys():
            if field not in org_data:
                org_data[field] = None

        # Step 2: Search only for missing fields
        missing_fields = [field for field, value in org_data.items() if value is None]

        if missing_fields:
            print(f"🔍 Found {len(missing_fields)} missing org fields: {missing_fields}")

            # Search for missing fields using plant name + field description
            additional_data = search_missing_fields(plant_name, missing_fields, ORG_SCHEMA_DESC, "organization")
            org_data.update(additional_data)

        filled_fields = len([v for v in org_data.values() if v is not None])
        print(f"✅ Organization: {filled_fields}/{len(ORG_SCHEMA_DESC)} fields filled")

        return org_data
    
    def process_plant(self, plant_name: str) -> Dict:
        """Process plant details using cached content first, then search for missing fields."""
        print("📊 Processing plant details...")

        # Step 1: Use cache memory to fill JSON first
        if cache_manager.has_cached_content():
            print("📁 Using cached content for plant data extraction...")
            cached_content = cache_manager.get_all_cached_content()
            plant_data = extract_data_with_llm(plant_name, cached_content, PLANT_SCHEMA_DESC, "plant")
        else:
            print("❌ No cached content available")
            plant_data = {}

        # Ensure all schema fields are present
        for field in PLANT_SCHEMA_DESC.keys():
            if field not in plant_data:
                plant_data[field] = None

        # Step 2: Search only for missing fields
        missing_fields = [field for field, value in plant_data.items() if value is None or value == [] or value == {}]

        if missing_fields:
            print(f"🔍 Found {len(missing_fields)} missing plant fields: {missing_fields}")

            # Search for missing fields using plant name + field description
            additional_data = search_missing_fields(plant_name, missing_fields, PLANT_SCHEMA_DESC, "plant")
            plant_data.update(additional_data)

        # Ensure we have units_id for next step
        if not plant_data.get('units_id') or plant_data['units_id'] == []:
            plant_data['units_id'] = [1]  # Minimal default

        filled_fields = len([v for v in plant_data.values() if v is not None and v != [] and v != {}])
        print(f"✅ Plant: {filled_fields}/{len(PLANT_SCHEMA_DESC)} fields filled")

        return plant_data
    
    def process_units(self, plant_name: str, plant_data: Dict) -> List[Dict]:
        """Process unit details - check cache first, then search for missing."""
        print("📊 Processing unit details...")
        
        # Get unit IDs from plant data
        unit_ids = plant_data.get('units_id', [1])
        if unit_ids:
            # Parse unit IDs from various formats
            parsed_unit_ids = []
            for uid in unit_ids:
                parsed_ids = self._parse_unit_id(uid)
                parsed_unit_ids.extend(parsed_ids)
            unit_ids = parsed_unit_ids if parsed_unit_ids else [1]
        
        units_data = []
        
        for unit_id in unit_ids:
            print(f"\n🔧 Processing Unit {unit_id}")

            # Step 1: Use cache memory to fill JSON first
            if cache_manager.has_cached_content():
                print("📁 Using cached content for unit data extraction...")
                cached_content = cache_manager.get_all_cached_content()
                unit_data = extract_data_with_llm(plant_name, cached_content, UNIT_SCHEMA_DESC, "unit")
            else:
                print("❌ No cached content available")
                unit_data = {}

            # Ensure all schema fields are present
            for field in UNIT_SCHEMA_DESC.keys():
                if field not in unit_data:
                    unit_data[field] = None

            # Set unit number
            unit_data['unit_number'] = str(unit_id)

            # Step 2: Search only for missing fields
            missing_fields = [field for field, value in unit_data.items() if (value is None or value == [] or value == {}) and field != 'unit_number']

            if missing_fields:
                print(f"🔍 Found {len(missing_fields)} missing unit fields: {missing_fields[:10]}...")

                # Search for missing fields using plant name + field description
                additional_data = search_missing_fields(plant_name, missing_fields, UNIT_SCHEMA_DESC, "unit")
                unit_data.update(additional_data)
                unit_data['unit_number'] = str(unit_id)  # Preserve unit number

            filled_fields = len([v for v in unit_data.values() if v is not None and v != [] and v != {}])
            print(f"✅ Unit {unit_id}: {filled_fields}/{len(UNIT_SCHEMA_DESC)} fields filled")

            units_data.append(unit_data)
        
        return units_data



    def _parse_unit_id(self, uid) -> List[int]:
        """Parse unit ID from various formats."""
        import re

        if isinstance(uid, int):
            return [uid]

        if not isinstance(uid, str):
            return [1]  # Default fallback

        uid_str = str(uid).lower()

        # Handle formats like "2 x 660 MW" -> extract the number before 'x'
        if 'x' in uid_str:
            match = re.search(r'(\d+)\s*x', uid_str)
            if match:
                count = int(match.group(1))
                return list(range(1, count + 1))  # [1, 2] for "2 x 660 MW"

        # Handle formats like "Unit 1, Unit 2" or "1, 2"
        if ',' in uid_str:
            parts = uid_str.split(',')
            unit_ids = []
            for part in parts:
                # Extract numbers from each part
                numbers = re.findall(r'\d+', part.strip())
                if numbers:
                    unit_ids.append(int(numbers[0]))
            return unit_ids if unit_ids else [1]

        # Handle single unit formats like "Unit 1", "1", "660 MW"
        numbers = re.findall(r'\d+', uid_str)
        if numbers:
            # If we find multiple numbers, take the first one that's likely a unit number
            for num in numbers:
                num_int = int(num)
                if 1 <= num_int <= 10:  # Reasonable unit number range
                    return [num_int]
            # If no reasonable unit number found and it's a capacity (like 660 MW), default to 1
            if any(unit in uid_str for unit in ['mw', 'kw', 'gw', 'capacity']):
                return [1]
            # Otherwise use the first number if it's reasonable
            first_num = int(numbers[0])
            if first_num <= 50:  # Reasonable unit count
                return [first_num]
            else:
                return [1]  # Default for large numbers (likely capacity)

        # Default fallback
        return [1]

    def save_results(self, plant_name: str, results: Dict):
        """Save results to JSON files."""
        
        # Save organization data
        org_file = os.path.join(self.output_dir, f"org_details_{plant_name}.json")
        with open(org_file, 'w', encoding='utf-8') as f:
            json.dump(results['organization'], f, indent=2, ensure_ascii=False)
        print(f"💾 Saved organization data: {org_file}")
        
        # Save plant data
        plant_file = os.path.join(self.output_dir, f"plant_details_{plant_name}.json")
        with open(plant_file, 'w', encoding='utf-8') as f:
            json.dump([results['plant']], f, indent=2, ensure_ascii=False)
        print(f"💾 Saved plant data: {plant_file}")
        
        # Save unit data
        unit_file = os.path.join(self.output_dir, f"unit_details_{plant_name}.json")
        with open(unit_file, 'w', encoding='utf-8') as f:
            json.dump(results['units'], f, indent=2, ensure_ascii=False)
        print(f"💾 Saved unit data: {unit_file}")

def main():
    """Main function to run the pipeline."""

    # Get plant name from user input
    plant_name = input("Enter power plant name: ").strip()

    if not plant_name:
        print("❌ Plant name is required!")
        return

    # Run pipeline
    pipeline = PowerPlantPipeline()
    results = pipeline.run_pipeline(plant_name)

    if results:
        print(f"\n✅ SUCCESS: Pipeline completed for {plant_name}")
        print(f"📁 Results saved in 'output/' directory")
    else:
        print(f"\n❌ FAILED: Pipeline failed for {plant_name}")

if __name__ == "__main__":
    main()
