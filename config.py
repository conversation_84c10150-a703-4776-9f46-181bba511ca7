#!/usr/bin/env python3
"""
Configuration file for the power plant pipeline.
Set your API keys here or use environment variables.
"""

import os

# API Keys - Set these or use environment variables
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '********************************************************************************************************************************************************************')
SCRAPER_API_KEY = os.getenv('SCRAPER_API_KEY', 'b4aedbdc7dc14b48930567434b5f3f2d')
GROQ_API_KEY = os.getenv('GROQ_API_KEY', '********************************************************')

# Model Configuration
OPENAI_MODEL = 'gpt-4o-mini'

# Search Configuration
DEFAULT_SEARCH_RESULTS = 5
SEARCH_TIMEOUT = 15
SCRAPE_TIMEOUT = 30

# Cache Configuration
CACHE_DIR = 'cache'
OUTPUT_DIR = 'output'

def get_api_keys():
    """Get API keys from environment or config."""
    return {
        'openai': OPENAI_API_KEY,
        'scraper': SCRAPER_API_KEY,
        'groq': GROQ_API_KEY
    }

def validate_config():
    """Validate that required configuration is present."""
    if not OPENAI_API_KEY:
        raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or update config.py")
    
    return True
